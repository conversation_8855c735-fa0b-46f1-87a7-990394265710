import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useN<PERSON>gate, <PERSON> } from 'react-router-dom';
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Checkbox,
  FormControlLabel
} from '@mui/material';
import { ArrowLeft, Save, Plus, Trash2 } from 'lucide-react';
import {
  getCommissionReportById,
  updateCommissionReport,
  addCommissionsToReport,
  removeCommissionsFromReport
} from '../../services/commissionReportService';
import {
  getAllEmployerCommissions
} from '../../services/employerCommissionService';
import {
  PAYMENT_STATUS_LABELS,
  REPORT_STATUS,
  REPORT_STATUS_LABELS,
  COMMISSION_STATUS,
  COMMISSION_PHASE_LABELS,
  EMPLOYMENT_TYPE_LABELS,
  LEVEL_LABELS,
  ENGLISH_SKILL_LABELS
} from '../../constants/collaboratorPolicy';
import { useToast } from "../../contexts/ToastContext";
import { formatCurrency } from '../../utils/currencyUtils';
import { formatDate, formatDateForInput, formatDateForAPI } from '../../utils/dateUtils';

const CommissionReportEditPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { showToast } = useToast();

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [reportData, setReportData] = useState(null);
  const [commissions, setCommissions] = useState([]);

  // Commission management state
  const [addCommissionDialogOpen, setAddCommissionDialogOpen] = useState(false);
  const [availableCommissions, setAvailableCommissions] = useState([]);
  const [selectedCommissionsToAdd, setSelectedCommissionsToAdd] = useState([]);
  const [loadingCommissions, setLoadingCommissions] = useState(false);
  const [commissionActionLoading, setCommissionActionLoading] = useState(false);

  const [formData, setFormData] = useState({
    employerEmail: '',
    name: '',
    month: '',
    year: '',
    totalAmount: '',
    status: '',
    notes: ''
  });

  const loadReportData = async () => {
    setLoading(true);
    try {
      const report = await getCommissionReportById(id);
      setReportData(report);
      setCommissions(report.commissions || []);
      setFormData({
        employerEmail: report.employerEmail || '',
        name: report.name || '',
        month: report.month || '',
        year: report.year || '',
        totalAmount: report.totalAmount || '',
        status: report.status || '',
        paymentStatus: report.paymentStatus || '',
        paidDate: formatDateForInput(report.paidDate),
        notes: report.notes || ''
      });
      setError(null);
    } catch (error) {
      console.error('Failed to load commission report:', error);
      setError('Failed to load commission report');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (id) {
      loadReportData();
    }
  }, [id]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Commission management functions
  const loadAvailableCommissions = async () => {
    if (!reportData?.employerEmail) return;

    setLoadingCommissions(true);
    try {
      const params = {
        page: -1,
        limit: -1,
        employerEmail: reportData.employerEmail,
        status: COMMISSION_STATUS.APPROVED,
        report: null
      };
      const response = await getAllEmployerCommissions(params);
      setAvailableCommissions(response.items);
      setSelectedCommissionsToAdd([]);
    } catch (error) {
      console.error('Failed to load available commissions:', error);
      showToast('Failed to load available commissions', 'error');
    } finally {
      setLoadingCommissions(false);
    }
  };

  const handleAddCommissions = async () => {
    if (selectedCommissionsToAdd.length === 0) return;

    setCommissionActionLoading(true);
    try {
      await addCommissionsToReport(id, selectedCommissionsToAdd.map(c => c.id));
      showToast('Commissions added successfully', 'success');
      setAddCommissionDialogOpen(false);
      loadReportData(); // Reload report data
    } catch (error) {
      console.error('Failed to add commissions:', error);
      showToast('Failed to add commissions', 'error');
    } finally {
      setCommissionActionLoading(false);
    }
  };

  const handleRemoveCommission = async (commissionId) => {
    setCommissionActionLoading(true);
    try {
      await removeCommissionsFromReport(id, [commissionId]);
      showToast('Commission removed successfully', 'success');
      loadReportData(); // Reload report data
    } catch (error) {
      console.error('Failed to remove commission:', error);
      showToast('Failed to remove commission', 'error');
    } finally {
      setCommissionActionLoading(false);
    }
  };

  const handleCommissionToggle = (commission) => {
    setSelectedCommissionsToAdd(prev => {
      const isSelected = prev.find(c => c.id === commission.id);
      if (isSelected) {
        return prev.filter(c => c.id !== commission.id);
      } else {
        return [...prev, commission];
      }
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);
    setError(null);

    try {
      const updateData = {
        name: formData.name,
        status: formData.status,
        paymentStatus: formData.paymentStatus,
        paidDate: formatDateForAPI(formData.paidDate),
        notes: formData.notes
      };

      await updateCommissionReport(id, updateData);
      setSuccess(true);
      showToast('Commission report updated successfully', 'success');

      // Redirect after a short delay
      setTimeout(() => {
        navigate(`/commission-reports/${id}`);
      }, 1500);
    } catch (error) {
      console.error('Failed to update commission report:', error);
      setError('Failed to update commission report');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="admin-layout">
        <div className="admin-content">
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
            <CircularProgress />
          </Box>
        </div>
      </div>
    );
  }

  return (
    <div className="admin-layout">
      <div className="admin-content">
        <Box sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h5" component="h1">
              Edit Commission Report
            </Typography>
            <Button
              variant="outlined"
              startIcon={<ArrowLeft />}
              onClick={() => navigate(`/commission-reports/${id}`)}
            >
              Back to Details
            </Button>
          </Box>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity="success" sx={{ mb: 2 }}>
              Commission report updated successfully! Redirecting...
            </Alert>
          )}

          <Paper sx={{ p: 3 }}>
            <form onSubmit={handleSubmit}>
              <Grid container spacing={3}>
                {/* Editable fields */}
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Report Name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    variant="outlined"
                  />
                </Grid>

                {/* Read-only fields */}
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Employer Email"
                    value={formData.employerEmail}
                    disabled
                    variant="outlined"
                  />
                </Grid>

                <Grid item xs={12} md={3}>
                  <TextField
                    fullWidth
                    label="Month"
                    value={formData.month}
                    disabled
                    variant="outlined"
                  />
                </Grid>

                <Grid item xs={12} md={3}>
                  <TextField
                    fullWidth
                    label="Year"
                    value={formData.year}
                    disabled
                    variant="outlined"
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Total Amount"
                    value={formatCurrency(formData.totalAmount)}
                    disabled
                    variant="outlined"
                  />
                </Grid>

                {/* Editable fields */}
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel>Status</InputLabel>
                    <Select
                      value={formData.status}
                      onChange={(e) => handleInputChange('status', e.target.value)}
                      label="Status"
                      disabled={saving}
                    >
                      {Object.entries(REPORT_STATUS_LABELS).map(([key, label]) => (
                        <MenuItem key={key} value={key}>
                          {label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel>Payment Status</InputLabel>
                    <Select
                      value={formData.paymentStatus}
                      onChange={(e) => handleInputChange('paymentStatus', e.target.value)}
                      label="Payment Status"
                      disabled={saving}
                    >
                      {Object.entries(PAYMENT_STATUS_LABELS).map(([key, label]) => (
                        <MenuItem key={key} value={key}>
                          {label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    label="Paid Date"
                    type="date"
                    value={formData.paidDate}
                    onChange={(e) => handleInputChange('paidDate', e.target.value)}
                    fullWidth
                    InputLabelProps={{
                      shrink: true,
                    }}
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Notes"
                    multiline
                    rows={4}
                    value={formData.notes}
                    onChange={(e) => handleInputChange('notes', e.target.value)}
                    disabled={saving}
                    variant="outlined"
                  />
                </Grid>

                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                    <Button
                      variant="outlined"
                      onClick={() => navigate(`/commission-reports/${id}`)}
                      disabled={saving}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      variant="contained"
                      startIcon={<Save />}
                      disabled={saving}
                    >
                      {saving ? 'Saving...' : 'Save Changes'}
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </form>
          </Paper>

          {/* Information Note */}
          <Paper sx={{ p: 2, mt: 2, bgcolor: 'info.light', color: 'info.contrastText' }}>
            <Typography variant="body2">
              <strong>Note:</strong> Only the status and notes fields can be edited.
              The employer email, month/year, and total amount are read-only to maintain data integrity.
            </Typography>
          </Paper>

          {/* Related Commissions Section */}
          {reportData && (
            <Paper sx={{ p: 3, mt: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  Related Commissions ({commissions.length})
                </Typography>
                {reportData.status === REPORT_STATUS.PENDING && (
                  <Button
                    variant="contained"
                    startIcon={<Plus />}
                    onClick={() => {
                      loadAvailableCommissions();
                      setAddCommissionDialogOpen(true);
                    }}
                    disabled={commissionActionLoading}
                  >
                    Add Commissions
                  </Button>
                )}
              </Box>
              <Divider sx={{ mb: 2 }} />

              {commissions.length > 0 ? (
                <Table>
                  <TableHead>
                    <TableRow>
                      {reportData.status === REPORT_STATUS.PENDING && <TableCell width="60">Actions</TableCell>}
                      <TableCell>Applicant</TableCell>
                      <TableCell>Employment Type</TableCell>
                      <TableCell>Level</TableCell>
                      <TableCell>English Skill</TableCell>
                      <TableCell>Phase</TableCell>
                      <TableCell align="right">Amount</TableCell>
                      <TableCell>Created Date</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {commissions.map((commission) => (
                      <TableRow key={commission.id} hover>
                        {reportData.status === REPORT_STATUS.PENDING && (
                          <TableCell>
                            <Tooltip title="Remove Commission">
                              <IconButton
                                size="small"
                                color="error"
                                onClick={() => handleRemoveCommission(commission.id)}
                                disabled={commissionActionLoading}
                              >
                                <Trash2 size={16} />
                              </IconButton>
                            </Tooltip>
                          </TableCell>
                        )}
                        <TableCell>
                          {commission.applicantName || '-'}
                        </TableCell>
                        <TableCell>{EMPLOYMENT_TYPE_LABELS[commission.employmentType]}</TableCell>
                        <TableCell>
                          {commission.candidateLevel ? LEVEL_LABELS[commission.candidateLevel] : '-'}
                        </TableCell>
                        <TableCell>
                          {commission.englishSkill ? ENGLISH_SKILL_LABELS[commission.englishSkill] : '-'}
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={COMMISSION_PHASE_LABELS[commission.commissionPhase]}
                            color="info"
                            size="small"
                          />
                        </TableCell>
                        <TableCell align="right">
                          <Typography variant="body2" color="primary" fontWeight="medium">
                            {formatCurrency(commission.amount)}
                          </Typography>
                        </TableCell>
                        <TableCell>{formatDate(commission.createdAt)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <Alert severity="info">
                  No commissions found for this report.
                </Alert>
              )}
            </Paper>
          )}

          {/* Add Commissions Dialog */}
          <Dialog
            open={addCommissionDialogOpen}
            onClose={() => setAddCommissionDialogOpen(false)}
            maxWidth="lg"
            fullWidth
          >
            <DialogTitle>
              Add Commissions to Report
            </DialogTitle>

            <DialogContent>
              {loadingCommissions ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                  <CircularProgress />
                </Box>
              ) : (
                <>
                  <Typography variant="body2" color="textSecondary" sx={{ mb: 2 }}>
                    Select commissions to add to this report. Only approved commissions that are not already in any report are shown.
                  </Typography>

                  {availableCommissions.length > 0 ? (
                    <Paper sx={{ maxHeight: 400, overflow: 'auto' }}>
                      <Table stickyHeader>
                        <TableHead>
                          <TableRow>
                            <TableCell padding="checkbox">Select</TableCell>
                            <TableCell>Applicant</TableCell>
                            <TableCell>Employment Type</TableCell>
                            <TableCell>Level</TableCell>
                            <TableCell>English Skill</TableCell>
                            <TableCell>Phase</TableCell>
                            <TableCell>Amount</TableCell>
                            <TableCell>Created Date</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {availableCommissions.map((commission) => (
                            <TableRow key={commission.id}>
                              <TableCell padding="checkbox">
                                <Checkbox
                                  checked={selectedCommissionsToAdd.some(c => c.id === commission.id)}
                                  onChange={() => handleCommissionToggle(commission)}
                                />
                              </TableCell>
                              <TableCell>
                                {commission.applicantName || '-'}
                              </TableCell>
                              <TableCell>{EMPLOYMENT_TYPE_LABELS[commission.employmentType]}</TableCell>
                              <TableCell>
                                {commission.candidateLevel ? LEVEL_LABELS[commission.candidateLevel] : '-'}
                              </TableCell>
                              <TableCell>
                                {commission.englishSkill ? ENGLISH_SKILL_LABELS[commission.englishSkill] : '-'}
                              </TableCell>
                              <TableCell>
                                <Chip
                                  label={COMMISSION_PHASE_LABELS[commission.commissionPhase]}
                                  color="info"
                                  size="small"
                                />
                              </TableCell>
                              <TableCell>{formatCurrency(commission.amount)}</TableCell>
                              <TableCell>{formatDate(commission.createdAt)}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </Paper>
                  ) : (
                    <Alert severity="info">
                      No available commissions found for this employer.
                    </Alert>
                  )}

                  {selectedCommissionsToAdd.length > 0 && (
                    <Box sx={{ mt: 2, p: 2, bgcolor: 'background.paper', border: 1, borderColor: 'divider', borderRadius: 1 }}>
                      <Typography variant="body2" color="textSecondary">
                        Selected: {selectedCommissionsToAdd.length} commission(s)
                      </Typography>
                      <Typography variant="body2" color="primary" fontWeight="medium">
                        Total Amount: {formatCurrency(selectedCommissionsToAdd.reduce((sum, c) => sum + Number(c.amount), 0))}
                      </Typography>
                    </Box>
                  )}
                </>
              )}
            </DialogContent>

            <DialogActions>
              <Button
                onClick={() => setAddCommissionDialogOpen(false)}
                disabled={commissionActionLoading}
              >
                Cancel
              </Button>
              <Button
                onClick={handleAddCommissions}
                variant="contained"
                disabled={commissionActionLoading || selectedCommissionsToAdd.length === 0}
              >
                {commissionActionLoading ? 'Adding...' : `Add ${selectedCommissionsToAdd.length} Commission(s)`}
              </Button>
            </DialogActions>
          </Dialog>
        </Box>
      </div>
    </div>
  );
};

export default CommissionReportEditPage;
