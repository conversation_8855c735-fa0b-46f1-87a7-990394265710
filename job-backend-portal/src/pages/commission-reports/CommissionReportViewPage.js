import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import {
  Box,
  Paper,
  Typography,
  Button,
  Grid,
  Chip,
  CircularProgress,
  Alert,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow
} from '@mui/material';
import { ArrowLeft, Edit, CheckCircle, DollarSign } from 'lucide-react';
import {
  getCommissionReportById,
  approveCommissionReport,
  markCommissionReportAsPaid
} from '../../services/commissionReportService';
import {
  REPORT_STATUS,
  REPORT_STATUS_LABELS,
  COMMISSION_PHASE_LABELS,
  EMPLOYMENT_TYPE_LABELS,
  LEVEL_LABELS,
  ENGLISH_SKILL_LABELS,
  PAYMENT_STATUS,
  PAYMENT_STATUS_LABELS
} from '../../constants/collaboratorPolicy';
import { formatDate } from '../../utils/dateUtils';
import { formatCurrency } from '../../utils/currencyUtils';
import { useToast } from "../../contexts/ToastContext";
import CommissionReportCreateDialog from './CommissionReportCreateDialog';

const CommissionReportViewPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { showToast } = useToast();
  
  const [report, setReport] = useState(null);
  const [commissions, setCommissions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [actionLoading, setActionLoading] = useState(false);
  const [manageCommissionsOpen, setManageCommissionsOpen] = useState(false);

  const getStatusColor = (status) => {
    switch (status) {
      case REPORT_STATUS.PENDING:
        return 'warning';
      case REPORT_STATUS.APPROVED:
        return 'info';
      case REPORT_STATUS.PAID:
        return 'success';
      case REPORT_STATUS.CANCELLED:
        return 'error';
      default:
        return 'default';
    }
  };

  const loadReportData = async () => {
    setLoading(true);
    try {
      const reportData = await getCommissionReportById(id);
      setReport(reportData);

      if (reportData.commissions) {
        setCommissions(reportData.commissions);
      }

      setError(null);
    } catch (error) {
      console.error('Failed to load commission report:', error);
      setError('Failed to load commission report');
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async () => {
    setActionLoading(true);
    try {
      await approveCommissionReport(id);
      showToast('Commission report approved successfully', 'success');
      loadReportData();
    } catch (error) {
      console.error('Failed to approve commission report:', error);
      showToast('Failed to approve commission report', 'error');
    } finally {
      setActionLoading(false);
    }
  };

  const handleMarkAsPaid = async () => {
    setActionLoading(true);
    try {
      await markCommissionReportAsPaid(id);
      showToast('Commission report marked as paid successfully', 'success');
      loadReportData();
    } catch (error) {
      console.error('Failed to mark commission report as paid:', error);
      showToast('Failed to mark commission report as paid', 'error');
    } finally {
      setActionLoading(false);
    }
  };

  useEffect(() => {
    if (id) {
      loadReportData();
    }
  }, [id]);

  if (loading) {
    return (
      <div className="admin-layout">
        <div className="admin-content">
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', p: 3 }}>
            <CircularProgress />
          </Box>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="admin-layout">
        <div className="admin-content">
          <Box sx={{ p: 3 }}>
            <Alert severity="error">{error}</Alert>
          </Box>
        </div>
      </div>
    );
  }

  return (
    <div className="admin-layout">
      <div className="admin-content">
        <Box sx={{ p: 3 }}>
          {/* Header Section */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h5" component="h1">
              Commission Report Details
            </Typography>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant="outlined"
                startIcon={<ArrowLeft />}
                component={Link}
                to="/commission-reports"
              >
                Back to List
              </Button>
              {report?.status === REPORT_STATUS.PENDING && (
                <Button
                  variant="contained"
                  color="success"
                  startIcon={<CheckCircle />}
                  onClick={handleApprove}
                  disabled={actionLoading}
                >
                  {actionLoading ? 'Approving...' : 'Approve'}
                </Button>
              )}
              {report?.status === REPORT_STATUS.APPROVED && report.paymentStatus !== PAYMENT_STATUS.PAID && (
                <Button
                  variant="contained"
                  color="info"
                  startIcon={<DollarSign />}
                  onClick={handleMarkAsPaid}
                  disabled={actionLoading}
                >
                  {actionLoading ? 'Processing...' : 'Mark as Paid'}
                </Button>
              )}
              {report?.status === REPORT_STATUS.PENDING && (
                <Button
                  variant="outlined"
                  onClick={() => setManageCommissionsOpen(true)}
                >
                  Manage Commissions
                </Button>
              )}
              <Button
                variant="contained"
                startIcon={<Edit />}
                component={Link}
                to={`/commission-reports/${id}/edit`}
              >
                Edit Report
              </Button>
            </Box>
          </Box>

          {/* Main Content */}
          <Paper sx={{ p: 3 }}>
            <Grid container spacing={3}>
              {/* Basic Information Section */}
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Basic Information
                </Typography>
                <Divider sx={{ mb: 2 }} />
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Report Name
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {report?.name || 'N/A'}
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Employer Email
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {report?.employerEmail}
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Month/Year
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {report?.month}/{report?.year}
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Total Commissions
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {commissions.length} commission(s)
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Total Amount
                </Typography>
                <Typography variant="h6" color="primary" sx={{ mb: 2 }}>
                  {formatCurrency(report?.totalAmount)}
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Status
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <Chip
                    label={REPORT_STATUS_LABELS[report?.status]}
                    color={getStatusColor(report?.status)}
                    size="small"
                  />
                </Box>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Payment Status
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <Chip
                    label={PAYMENT_STATUS_LABELS[report?.paymentStatus]}
                    color={report?.paymentStatus === 'Paid' ? 'success' : 'warning'}
                    size="small"
                  />
                </Box>
              </Grid>

              {report?.paidDate && (
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Paid Date
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 2 }}>
                    {formatDate(report.paidDate)}
                  </Typography>
                </Grid>
              )}

              <Grid item xs={12}>
                <Typography variant="subtitle2" color="text.secondary">
                  Notes
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {report?.notes || 'No notes available'}
                </Typography>
              </Grid>
            </Grid>
          </Paper>

          {/* Related Commissions Section */}
          <Paper sx={{ p: 3, mt: 3 }}>
            <Typography variant="h6" gutterBottom>
              Related Commissions ({commissions.length})
            </Typography>
            <Divider sx={{ mb: 2 }} />
            
            {commissions.length > 0 ? (
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Applicant</TableCell>
                    <TableCell>Employment Type</TableCell>
                    <TableCell>Level</TableCell>
                    <TableCell>English Skill</TableCell>
                    <TableCell>Phase</TableCell>
                    <TableCell align="right">Amount</TableCell>
                    <TableCell>Created Date</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {commissions.map((commission) => (
                    <TableRow 
                      key={commission.id} 
                      hover
                      sx={{ cursor: 'pointer' }}
                      onDoubleClick={() => navigate(`/employer-commissions/${commission.id}`)}
                    >
                      <TableCell>
                        {commission.applicantName && (
                          <Link
                            to={`/applicants/${commission.jobApplicationId}`}
                            style={{ textDecoration: 'none' }}
                          >
                            {commission.applicantName}
                          </Link>
                        )}
                      </TableCell>
                      <TableCell>{EMPLOYMENT_TYPE_LABELS[commission.employmentType]}</TableCell>
                      <TableCell>
                        {commission.candidateLevel ? LEVEL_LABELS[commission.candidateLevel] : '-'}
                      </TableCell>
                      <TableCell>
                        {commission.englishSkill ? ENGLISH_SKILL_LABELS[commission.englishSkill] : '-'}
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={COMMISSION_PHASE_LABELS[commission.commissionPhase]}
                          color="info"
                          size="small"
                        />
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body2" color="primary" fontWeight="medium">
                          {formatCurrency(commission.amount)}
                        </Typography>
                      </TableCell>
                      <TableCell>{formatDate(commission.createdAt)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <Alert severity="info">
                No commissions found for this report.
              </Alert>
            )}
          </Paper>

          <Paper sx={{ p: 3, mt: 3 }}>
            <Typography variant="h6" gutterBottom>
              Audit Information
            </Typography>
            <Divider sx={{ mb: 2 }} />
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Approved By
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {report?.approvedBy || 'Not approved yet'}
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Approved At
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {report?.approvedAt ? formatDate(report.approvedAt) : 'Not approved yet'}
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Created Date
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {formatDate(report?.createdAt)}
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Updated Date
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {formatDate(report?.updatedAt)}
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Created By
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {report?.createdBy || 'System'}
                </Typography>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Updated By
                </Typography>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {report?.updatedBy || 'System'}
                </Typography>
              </Grid>
            </Grid>
          </Paper>

          {/* Manage Commissions Dialog */}
          <CommissionReportCreateDialog
            open={manageCommissionsOpen}
            onClose={() => setManageCommissionsOpen(false)}
            onSuccess={() => {
              setManageCommissionsOpen(false);
              loadReportData(); // Reload the report data
            }}
            mode="edit"
            reportData={report}
            existingCommissions={commissions}
          />
        </Box>
      </div>
    </div>
  );
};

export default CommissionReportViewPage;