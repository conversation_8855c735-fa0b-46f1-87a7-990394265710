import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Typography,
  Button,
  TextField,
  Switch,
  Pagination,
  CircularProgress,
  Alert,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Grid,
  PaginationItem,
  Tooltip,
  IconButton
} from '@mui/material';
import { Plus, AlertTriangle, Edit, Trash2 } from 'lucide-react';
import { getOptions, seedOptions } from '../../services/optionService';
import './OptionsPage.css';

const OPTION_TYPES = [
  'JOB_TYPES',
  'JOB_CATEGORIES',
  'JOB_LOCATIONS',
  'JOB_SALARY_RANGES',
  'JOB_WORKING_HOURS'
];

function OptionsPage() {
  const [options, setOptions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [appliedSearchTerm, setAppliedSearchTerm] = useState("")
  const [selectedOptionType, setSelectedOptionType] = useState('');
  const [page, setPage] = useState(1);
  const [seedLoading, setSeedLoading] = useState(false);
  const [seedSuccess, setSeedSuccess] = useState(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [totalPages, setTotalPages] = useState(1);

  const itemsPerPage = 10;

  useEffect(() => {
    fetchOptions();
  }, [page, itemsPerPage, appliedSearchTerm, selectedOptionType]);

  const fetchOptions = async () => {
    try {
      setLoading(true);
      const params = {
        page,
        limit: itemsPerPage,
        search: searchTerm,
        optionNames: selectedOptionType || undefined
      };

      const response = await getOptions(params);
      setOptions(response.items);
      setTotalPages(response.meta.totalPages);
      setError(null);
    } catch (err) {
      setError('Failed to load options');
      console.error('Error loading options:', err);
    } finally {
      setLoading(false);
    }
  };

  const openSeedConfirmDialog = () => {
    setConfirmDialogOpen(true);
  };

  const closeSeedConfirmDialog = () => {
    setConfirmDialogOpen(false);
  };

  const handleSeedOptions = async () => {
    try {
      setSeedLoading(true);
      await seedOptions();
      setSeedSuccess(true);
      fetchOptions();
      setTimeout(() => setSeedSuccess(false), 3000);
      closeSeedConfirmDialog();
    } catch (err) {
      setError('Failed to seed options');
      console.error('Error seeding options:', err);
    } finally {
      setSeedLoading(false);
    }
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 5 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <div className="admin-layout">
      <div className="admin-content">
        <Box sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
            <Typography variant="h5" component="h1">
              Options Management
            </Typography>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant="outlined"
                color="primary"
                onClick={openSeedConfirmDialog}
                disabled={seedLoading}
              >
                {seedLoading ? <CircularProgress size={24} /> : 'Seed Options'}
              </Button>
              <Button
                variant="contained"
                color="primary"
                startIcon={<Plus />}
                component={Link}
                to="/options/create"
              >
                Add New Option
              </Button>
            </Box>
          </Box>

          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {seedSuccess && (
            <Alert severity="success" sx={{ mb: 3 }}>
              Options seeded successfully!
            </Alert>
          )}

          <Paper sx={{ p: 2, mb: 3 }}>
            <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
              <TextField
                label="Search Options"
                variant="outlined"
                size="small"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    setAppliedSearchTerm(searchTerm);
                  }
                }}
                sx={{ flexGrow: 1 }}
              />
              <Button
                onClick={() => fetchOptions()}
                variant="contained"
                size="small"
              >
                Search
              </Button>
              {/* <FormControl size="small" sx={{ minWidth: 200 }}>
                <InputLabel>Option Type</InputLabel>
                <Select
                  value={selectedOptionType}
                  onChange={(e) => setSelectedOptionType(e.target.value)}
                  label="Option Type"
                >
                  <MenuItem value="">All Types</MenuItem>
                  {OPTION_TYPES.map((type) => (
                    <MenuItem key={type} value={type}>
                      {type}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl> */}
            </Box>

            <Grid container spacing={2} sx={{ mb: 2 }}>
              <Grid item xs={12} sm={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>Option Filter</InputLabel>
                  <Select
                    value={selectedOptionType}
                    onChange={(e) => setSelectedOptionType(e.target.value)}
                    label="Option Filter"
                  >
                    {OPTION_TYPES.map((type) => (
                      <MenuItem key={type} value={type}>
                        {type}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            </Grid>

            <Box sx={{ width: '100%', overflowX: 'auto' }}>
              <Table sx={{ minWidth: 800 }}>
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ fontWeight: 'bold' }}>Actions</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Option Name</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Option Title</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Option Code</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Index</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Status</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {options.map((option) => (
                    <TableRow key={option.id}>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 0.5 }}>
                          <Tooltip title="Edit">
                            <IconButton
                              size="small"
                              component={Link}
                              to={`/options/${option.id}`}
                            >
                              <Edit size={16} />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Delete">
                            <IconButton
                              size="small"
                              color="error"
                              component={Link}
                              to={`/options/${option.id}/delete`}
                            >
                              <Trash2 size={16} />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Link
                          to={`/options/${option.id}/view`}
                          style={{
                            color: '#1976d2',
                            textDecoration: 'none',
                            fontWeight: 'medium'
                          }}
                        >
                          {option.optionName}
                        </Link>
                      </TableCell>
                      <TableCell>{option.optionTitle}</TableCell>
                      <TableCell>{option.optionCode}</TableCell>
                      <TableCell>{option.index}</TableCell>
                      <TableCell>
                        <Switch
                          checked={option.active}
                          disabled
                          color="primary"
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </Box>

            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
              <Pagination
                count={totalPages}
                page={page}
                onChange={handleChangePage}
                color="primary"
                shape="rounded"
                size="small"
                type="first"
                showFirstButton
                showLastButton
                siblingCount={3}
              />
            </Box>
          </Paper>
        </Box>
      </div>

      {/* Confirmation Dialog for Seed Options */}
      <Dialog
        open={confirmDialogOpen}
        onClose={closeSeedConfirmDialog}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <AlertTriangle color="#f44336" size={24} />
            <Typography variant="h6">Confirm Seed Options</Typography>
          </Box>
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            This action will replace all existing options with default values. Any custom options you have created will be lost. Are you sure you want to proceed?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={closeSeedConfirmDialog} disabled={seedLoading}>
            Cancel
          </Button>
          <Button
            onClick={handleSeedOptions}
            color="error"
            variant="contained"
            disabled={seedLoading}
            autoFocus
          >
            {seedLoading ? <CircularProgress size={24} /> : 'Seed Options'}
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}

export default OptionsPage;
