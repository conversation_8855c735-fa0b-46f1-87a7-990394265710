import axiosInstance from '../utils/axios';
import { API_ENDPOINTS } from '../constants/api';

export const getAllCommissionReports = async (params = {}) => {
  try {
    const response = await axiosInstance.get(API_ENDPOINTS.COMMISSION_REPORTS, { params });
    return response.data;
  } catch (error) {
    console.error('Error fetching commission reports:', error);
    throw error;
  }
};

export const getCommissionReportById = async (id) => {
  try {
    const response = await axiosInstance.get(`${API_ENDPOINTS.COMMISSION_REPORTS}/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching commission report by id:', error);
    throw error;
  }
};

export const createCommissionReport = async (reportData) => {
  try {
    const response = await axiosInstance.post(API_ENDPOINTS.COMMISSION_REPORTS, reportData);
    return response.data;
  } catch (error) {
    console.error('Error creating commission report:', error);
    throw error;
  }
};

export const updateCommissionReport = async (id, reportData) => {
  try {
    const response = await axiosInstance.put(`${API_ENDPOINTS.COMMISSION_REPORTS}/${id}`, reportData);
    return response.data;
  } catch (error) {
    console.error('Error updating commission report:', error);
    throw error;
  }
};

export const deleteCommissionReport = async (id) => {
  try {
    const response = await axiosInstance.delete(`${API_ENDPOINTS.COMMISSION_REPORTS}/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error deleting commission report:', error);
    throw error;
  }
};

export const approveCommissionReport = async (id, approveData = {}) => {
  try {
    const response = await axiosInstance.patch(`${API_ENDPOINTS.COMMISSION_REPORTS}/${id}/approve`, approveData);
    return response.data;
  } catch (error) {
    console.error('Error approving commission report:', error);
    throw error;
  }
};

export const updatePaymentStatus = async (id, paymentData) => {
  try {
    const response = await axiosInstance.patch(`${API_ENDPOINTS.COMMISSION_REPORTS}/${id}/payment-status`, paymentData);
    return response.data;
  } catch (error) {
    console.error('Error updating payment status:', error);
    throw error;
  }
};

export const markCommissionReportAsPaid = async (id) => {
  try {
    const response = await axiosInstance.patch(`${API_ENDPOINTS.COMMISSION_REPORTS}/${id}/mark-paid`);
    return response.data;
  } catch (error) {
    console.error('Error marking commission report as paid:', error);
    throw error;
  }
};

export const getEmployerEmails = async () => {
  try {
    const response = await axiosInstance.get(`${API_ENDPOINTS.COMMISSION_REPORTS}/employer-emails`);
    return response.data;
  } catch (error) {
    console.error('Error fetching employer emails:', error);
    throw error;
  }
};

export const addCommissionsToReport = async (reportId, commissionIds) => {
  try {
    const response = await axiosInstance.patch(`${API_ENDPOINTS.COMMISSION_REPORTS}/${reportId}/add-commissions`, {
      commissionIds
    });
    return response.data;
  } catch (error) {
    console.error('Error adding commissions to report:', error);
    throw error;
  }
};

export const removeCommissionsFromReport = async (reportId, commissionIds) => {
  try {
    const response = await axiosInstance.patch(`${API_ENDPOINTS.COMMISSION_REPORTS}/${reportId}/remove-commissions`, {
      commissionIds
    });
    return response.data;
  } catch (error) {
    console.error('Error removing commissions from report:', error);
    throw error;
  }
};
