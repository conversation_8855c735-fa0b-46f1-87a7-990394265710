import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { ContextService } from '../../common/services/context.service';
import { CommissionReport } from '../entities/commission-report.entity';
import { EmployerCommission } from '../entities/employer-commission.entity';
import { CreateCommissionReportDto } from '../dto/create-commission-report.dto';
import { UpdateCommissionReportDto } from '../dto/update-commission-report.dto';
import { FindCommissionReportDto } from '../dto/find-commission-report.dto';
import { ApproveCommissionReportDto } from '../dto/approve-commission-report.dto';
import { PaginatedResult } from '../../common/dto/pagination.dto';
import { ReportStatus } from '../enums/report-status.enum';
import { PaymentStatus } from '../enums/payment-status.enum';
import { CommissionStatus } from '../enums/commission-status.enum';
import { BaseAuditRepository } from '../../common/repositories/base-audit.repository';
import { AuditRepositoryFactory } from '../../common/services/audit-repository-factory.service';
import { UpdatePaymentStatusDto } from '../dto/update-payment-status.dto';

@Injectable()
export class CommissionReportService {
  private readonly commissionReportRepository: BaseAuditRepository<CommissionReport>;
  private readonly employerCommissionRepository: BaseAuditRepository<EmployerCommission>;
  
  constructor(
    private readonly auditFactory: AuditRepositoryFactory,
    protected readonly context: ContextService,
  ) {
    this.commissionReportRepository = this.auditFactory.createRepository(CommissionReport);
    this.employerCommissionRepository = this.auditFactory.createRepository(EmployerCommission);
  }

  async create(createCommissionReportDto: CreateCommissionReportDto): Promise<CommissionReport> {
    const { commissionIds, ...reportData } = createCommissionReportDto;

    // Validate commissions exist and are approved
    const commissions = await this.employerCommissionRepository.find({
      where: {
        id: In(commissionIds),
        employerEmail: createCommissionReportDto.employerEmail,
        status: CommissionStatus.APPROVED,
        report: undefined
      }
    });

    if (commissions.length !== commissionIds.length) {
      throw new BadRequestException('Some commissions are not found, not approved, or already processed');
    }

    const totalAmount = commissions.reduce((sum, commission) => sum + Number(commission.amount), 0);

    const report = this.commissionReportRepository.create({
      ...reportData,
      totalAmount,
      status: ReportStatus.PENDING,
      paymentStatus: PaymentStatus.UNPAID
    });

    const savedReport = await this.commissionReportRepository.save(report);

    await this.employerCommissionRepository.update(
      { id: In(commissions.map(c => c.id)) },
      { report: savedReport }
    );

    return savedReport;
  }

  async findAll(filters: FindCommissionReportDto): Promise<PaginatedResult<CommissionReport>> {
    const { page = 1, limit = 10, search, employerEmail, name, month, year, status } = filters;
    
    const queryBuilder = this.commissionReportRepository.createQueryBuilder('report');

    if (search) {
      queryBuilder.andWhere(
        '(report.employerEmail LIKE :search OR report.name LIKE :search OR report.notes LIKE :search)',
        { search: `%${search}%` }
      );
    }

    if (employerEmail) {
      queryBuilder.andWhere('report.employerEmail = :employerEmail', { employerEmail });
    }

    if (name) {
      queryBuilder.andWhere('report.name LIKE :name', { name: `%${name}%` });
    }

    if (month) {
      queryBuilder.andWhere('report.month = :month', { month });
    }

    if (year) {
      queryBuilder.andWhere('report.year = :year', { year });
    }

    if (status) {
      queryBuilder.andWhere('report.status = :status', { status });
    }
    
    // Sorting
    const sortBy = filters?.sortBy || 'createdAt';
    const sortOrder = filters?.sortOrder || 'DESC';
    queryBuilder.orderBy(`report.${sortBy}`, sortOrder);

    const totalCount = await queryBuilder.getCount();

    // Pagination
    if (limit !== -1) {
      queryBuilder.limit(limit);
      if (page !== -1) {
        queryBuilder.offset((page - 1) * limit);
      }
    }
    const results = await queryBuilder.getMany();

    return new PaginatedResult(results, totalCount, page, limit);
  }

  async findOne(id: number): Promise<CommissionReport> {
    const report = await this.commissionReportRepository.findOne({
      where: { id },
      relations: ['commissions']
    });

    if (!report) {
      throw new NotFoundException(`Commission report with ID "${id}" not found`);
    }

  const enrichedCommissions = await this.getCommissionApplicantInfo(report.commissions);
  report.commissions = enrichedCommissions;

    return report;
  }

  async update(id: number, dto: UpdateCommissionReportDto): Promise<CommissionReport> {
    const report = await this.findOne(id);

    Object.assign(report, dto);
    if (report.status === ReportStatus.APPROVED) {
      report.approvedBy = this.context.user?.email || '';
      report.approvedAt = new Date();
    }
    if (report.paymentStatus === PaymentStatus.PAID && !report.paidDate) {
      report.paidDate = new Date();
    }
    
    return this.commissionReportRepository.save(report);
  }

  async remove(id: number): Promise<void> {
    await this.employerCommissionRepository.manager.transaction(async manager => {
      const report = await manager.findOne(CommissionReport, { where: { id } });
      if (!report) {
        throw new NotFoundException('Report not found');
      }

      await manager.query(
        'UPDATE employer_commissions SET reportId = NULL WHERE reportId = ?',
        [id]
      );

      await manager.delete(CommissionReport, id);
    });
  }

  async approve(id: number, approveDto: ApproveCommissionReportDto): Promise<CommissionReport> {
    const report = await this.findOne(id);
    
    if (report.status !== ReportStatus.PENDING) {
      throw new BadRequestException('Only pending reports can be approved');
    }

    report.status = ReportStatus.APPROVED;
    report.approvedBy = this.context.user?.email || '';
    report.approvedAt = new Date();
    
    if (approveDto.notes) {
      report.notes = approveDto.notes;
    }

    return this.commissionReportRepository.save(report);
  }

  async markAsPaid(id: number): Promise<CommissionReport> {
    const report = await this.findOne(id);

    if (report.status !== ReportStatus.APPROVED) {
      throw new BadRequestException('Only approved reports can be marked as paid');
    }

    report.paymentStatus = PaymentStatus.PAID;
    report.paidDate = new Date();

    return this.commissionReportRepository.save(report);
  }

  async addCommissions(id: number, commissionIds: number[]): Promise<CommissionReport> {
    const report = await this.findOne(id);

    if (report.status !== ReportStatus.PENDING) {
      throw new BadRequestException('Only pending reports can have commissions added');
    }

    // Validate commissions exist and are approved
    const commissions = await this.employerCommissionRepository.find({
      where: {
        id: In(commissionIds),
        employerEmail: report.employerEmail,
        status: CommissionStatus.APPROVED,
        report: undefined
      }
    });

    if (commissions.length !== commissionIds.length) {
      throw new BadRequestException('Some commissions are not found, not approved, or already processed');
    }

    // Update commissions to link to this report
    await this.employerCommissionRepository.update(
      { id: In(commissionIds) },
      { report: report }
    );

    // Recalculate total amount
    const allCommissions = await this.employerCommissionRepository.find({
      where: { report: { id: report.id } }
    });

    const newTotalAmount = allCommissions.reduce((sum, commission) => sum + Number(commission.amount), 0);
    report.totalAmount = newTotalAmount;

    return this.commissionReportRepository.save(report);
  }

  async removeCommissions(id: number, commissionIds: number[]): Promise<CommissionReport> {
    const report = await this.findOne(id);

    if (report.status !== ReportStatus.PENDING) {
      throw new BadRequestException('Only pending reports can have commissions removed');
    }

    // Validate commissions belong to this report
    const commissions = await this.employerCommissionRepository.find({
      where: {
        id: In(commissionIds),
        report: { id: report.id }
      }
    });

    if (commissions.length !== commissionIds.length) {
      throw new BadRequestException('Some commissions are not found in this report');
    }

    // Remove commissions from this report
    await this.employerCommissionRepository.update(
      { id: In(commissionIds) },
      { report: null }
    );
    await manager.query(
        'UPDATE employer_commissions SET reportId = NULL WHERE reportId = ?',
        [id]
      );

    // Recalculate total amount
    const remainingCommissions = await this.employerCommissionRepository.find({
      where: { report: { id: report.id } }
    });

    const newTotalAmount = remainingCommissions.reduce((sum, commission) => sum + Number(commission.amount), 0);
    report.totalAmount = newTotalAmount;

    return this.commissionReportRepository.save(report);
  }

  async updatePaymentStatus(id: number, updatePaymentStatusDto: UpdatePaymentStatusDto): Promise<CommissionReport> {
    const report = await this.commissionReportRepository.findOne({
      where: { id }
    });

    if (!report) {
      throw new NotFoundException(`Commission report with ID "${id}" not found`);
    }

    report.paymentStatus = updatePaymentStatusDto.paymentStatus;
    if (updatePaymentStatusDto.paidDate) {
      report.paidDate = updatePaymentStatusDto.paidDate;
    }
    else {
      report.paidDate = new Date();
    }

    return this.commissionReportRepository.save(report);
  }

  async getEmployerEmails(): Promise<string[]> {
    const result = await this.employerCommissionRepository
      .createQueryBuilder('commission')
      .select('DISTINCT commission.employerEmail', 'employerEmail')
      .where('commission.status = :status', { status: CommissionStatus.APPROVED })
      .getRawMany();

    return result.map(item => item.employerEmail);
  }

  private async getCommissionApplicantInfo(commissions: EmployerCommission[]): Promise<any[]> {
  if (commissions.length === 0) return [];

  const commissionIds = commissions.map(c => c.id);

  const rawData = await this.employerCommissionRepository
    .createQueryBuilder('commission')
    .leftJoin('job_application', 'jobApp', 'jobApp.id = commission.jobApplicationId')
    .where('commission.id IN (:...ids)', { ids: commissionIds })
    .select([
      'commission.id as commissionId',
      'jobApp.name as applicantName'
    ])
    .getRawMany();

  const rawMap = new Map<number, { applicantName: string }>();
  rawData.forEach(item => {
    rawMap.set(item.commissionId, {
      applicantName: item.applicantName
    });
  });

  return commissions.map(c => ({
    ...c,
    applicantName: rawMap.get(c.id)?.applicantName ?? null
  }));
}

}
